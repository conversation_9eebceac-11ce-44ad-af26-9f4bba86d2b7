"""
ICP (迭代式上下文规划) 工具集 (V3.0 - 统一架构版)

实现"思考-行动-观察"循环所需的Planner Tools，
支持AI驱动的迭代式旅行规划。
"""

import json
import logging
import math
import random
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.tools.unified_registry import unified_registry
from src.models.poi import EnrichedPOI, BasicPOI, POIType, convert_basic_to_enriched
from src.core.llm_manager import LLMManager
from src.prompts.loader import get_travel_planner_prompt

logger = logging.getLogger(__name__)


def _generate_suggested_time(poi_type: str) -> str:
    """
    根据POI类型生成建议游览时间

    Args:
        poi_type: POI类型

    Returns:
        建议时间字符串
    """
    time_mapping = {
        "ATTRACTION": "上午 09:00 - 12:00",
        "RESTAURANT": "午餐 12:00 - 13:30",
        "HOTEL": "入住 15:00",
        "SHOPPING": "下午 14:00 - 17:00",
        "OTHER": "上午 10:00 - 11:30"
    }
    return time_mapping.get(poi_type, "上午 10:00 - 11:30")


def _generate_itinerary_summary(
    daily_activities: List[Dict[str, Any]]
) -> str:
    """
    生成当天已完成活动的简洁摘要

    Args:
        daily_activities: 当天已安排的活动列表

    Returns:
        格式化的行程摘要字符串
    """
    if not daily_activities:
        return "今天还没有安排任何活动"
    
    summary_items = []
    for activity in daily_activities:
        # 提取关键信息
        start_time = activity.get("start_time", "未知时间")
        end_time = activity.get("end_time", "")
        poi_name = activity.get("name") or activity.get("poi_details", {}).get("name", "未知地点")
        poi_type = activity.get("poi_type") or activity.get("type", "")
        
        # 格式化时间段
        if end_time and end_time != start_time:
            time_segment = f"{start_time}-{end_time}"
        else:
            time_segment = start_time
        
        # 添加类型信息（如果有）
        if poi_type:
            poi_display = f"{poi_name}({poi_type})"
        else:
            poi_display = poi_name
            
        summary_items.append(f"- {time_segment}: {poi_display}")
    
    return "; ".join(summary_items)


@unified_registry.register_planner_tool
async def generate_planning_thought(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    **LLM驱动的规划思考工具 (V3.0)**

    基于当前状态和规划上下文，调用LLM生成下一步的思考和行动指令。

    Args:
        current_state: 当前规划状态
        planning_context: 规划上下文

    Returns:
        LLM返回的结构化思考和行动指令
    """
    try:
        # 1. 准备Prompt上下文
        icp_state = current_state.get("icp_planner_state", {})
        current_day = icp_state.get("current_day", 1)
        current_time = icp_state.get("current_time", "09:00")
        current_location = icp_state.get("current_location", {})

        # 获取当前天的已有活动
        daily_activities = current_state.get("daily_plans", {}).get(current_day, [])

        # 生成当天已完成活动的摘要（新增）
        itinerary_summary = _generate_itinerary_summary(daily_activities)

        # 新增：用餐间隔检查
        meal_interval_info = _check_meal_interval(daily_activities, current_time)

        # 新增：活动强度分析
        activity_intensity_info = _analyze_activity_intensity(daily_activities, current_time)

        # 新增：饥饿感知检查
        hunger_info = _check_hunger_level(daily_activities, current_time)

        # P3核心改造：使用nearby_poi_options（已按距离排序），而不是随机采样
        nearby_poi_options = current_state.get("nearby_poi_options", [])

        # 智能POI过滤：多重过滤逻辑，考虑饥饿感知和早餐时间段
        filtered_poi_options = []
        can_eat = meal_interval_info.get("can_eat", True)
        should_eat = hunger_info.get("should_eat", False)

        # 解析当前时间，检查是否在早餐时间段
        from datetime import datetime
        current_obj = datetime.strptime(current_time, "%H:%M")
        current_minutes = current_obj.hour * 60 + current_obj.minute
        is_breakfast_time = current_minutes < 660  # 11:00之前为早餐时间段，禁止用餐

        print(f"[调试] POI过滤开始 - can_eat: {can_eat}, should_eat: {should_eat}, is_breakfast_time: {is_breakfast_time}, 原始POI数量: {len(nearby_poi_options)}")
        logger.info(f"POI过滤开始 - can_eat: {can_eat}, should_eat: {should_eat}, is_breakfast_time: {is_breakfast_time}, 原始POI数量: {len(nearby_poi_options)}")

        for poi in nearby_poi_options:
            poi_type = poi.get("poi_type", "").lower()
            poi_name = poi.get("name", "未知POI")

            # 过滤1：早餐时间段禁止用餐（假设酒店已提供早餐）
            if is_breakfast_time and poi_type == "restaurant":
                logger.info(f"过滤餐厅POI: {poi_name} (早餐时间段，假设酒店已提供早餐)")
                continue

            # 过滤2：如果不能用餐且这是餐厅，则跳过
            if not can_eat and poi_type == "restaurant":
                logger.info(f"过滤餐厅POI: {poi_name} (用餐间隔不足)")
                continue

            # 过滤3：检查是否为重复或相似的POI
            if _is_duplicate_or_similar_poi(poi, daily_activities):
                logger.info(f"过滤重复POI: {poi_name} (已访问相似地点)")
                continue

            filtered_poi_options.append(poi)

        logger.info(f"POI过滤完成 - 过滤后POI数量: {len(filtered_poi_options)}")

        # 如果应该用餐，重新排序以优先展示餐厅
        if should_eat and filtered_poi_options:
            restaurants = [poi for poi in filtered_poi_options if poi.get("poi_type", "").lower() == "restaurant"]
            non_restaurants = [poi for poi in filtered_poi_options if poi.get("poi_type", "").lower() != "restaurant"]
            if restaurants:
                filtered_poi_options = restaurants + non_restaurants
                print(f"[调试] 饥饿感知生效 - 重新排序，优先展示 {len(restaurants)} 个餐厅")

        # 为了防止Prompt过长，只取最近的5个POI作为思考选项
        poi_options_sample = filtered_poi_options[:5]

        # 如果过滤后没有POI，使用原始列表（但记录警告）
        if not poi_options_sample and nearby_poi_options:
            logger.warning("过滤后没有可用POI，使用原始列表")
            poi_options_sample = nearby_poi_options[:5]

        # 记录详细的调试信息
        logger.info(f"LLM思考上下文 - 第{current_day}天 {current_time}, 当前位置: {current_location.get('name', '未知')}, 已有活动: {len(daily_activities)}个")
        logger.info(f"LLM思考选项 (Top 5 最近): {[p.get('name') for p in poi_options_sample]}")
        logger.info(f"当天行程摘要: {itinerary_summary}")

        # 新增：检查已访问的相似POI，避免重复游览
        visited_poi_names = set()
        visited_poi_keywords = set()

        for activity in daily_activities:
            poi_name = activity.get("poi_details", {}).get("name") or activity.get("poi_name", "")
            if poi_name:
                visited_poi_names.add(poi_name.lower())
                # 提取关键词（如"故宫博物院"）
                for keyword in ["故宫", "天坛", "景山", "王府井", "全聚德"]:
                    if keyword in poi_name:
                        visited_poi_keywords.add(keyword)

        context_for_prompt = {
            "user_preferences": current_state.get("consolidated_intent", {}).get("preferences", {}),
            "planning_state": {
                "current_day": current_day,
                "current_time": current_time,
                "current_location": current_location,
                "daily_activities": daily_activities,
                # 新增：当天已完成行程摘要
                "itinerary_summary": itinerary_summary,
                # 新增：用餐间隔检查信息
                "meal_interval_info": meal_interval_info,
                # 新增：活动强度分析信息
                "activity_intensity_info": activity_intensity_info,
                # 新增：饥饿感知信息
                "hunger_info": hunger_info,
                # 新增：已访问POI信息，避免重复
                "visited_poi_info": {
                    "visited_names": list(visited_poi_names),
                    "visited_keywords": list(visited_poi_keywords),
                    "warning": "绝对禁止选择已访问过的POI或相似POI！"
                },
                # P3核心改造：传递更丰富的POI信息给LLM，让LLM智能估算游玩时间
                "nearby_poi_options": [
                    {
                        "name": p.get("name"),
                        "type": p.get("poi_type", "other"),
                        "rating": float(p.get("rating", 0)) if p.get("rating") else 0,
                        "distance_km": round(p.get("distance_km", float('inf')), 1),
                        "business_hours": p.get("business_hours") if p.get("business_hours") else "营业时间请咨询",
                        "address": p.get("address", "地址未知")[:50],  # 截断过长地址
                        "phone": p.get("phone", ""),
                        "price": p.get("price", ""),
                        # 智能时长估算提示
                        "time_estimation_hint": _get_time_estimation_hint(p.get("name", ""), p.get("poi_type", "")),
                        "display_text": f"{p.get('name', '未知')} (距离{round(p.get('distance_km', float('inf')), 1)}km, 评分{float(p.get('rating', 0)) if p.get('rating') else 0})"
                    }
                    for p in poi_options_sample
                ]
            }
        }

        # 2. 加载并格式化Prompt
        formatted_prompt = get_travel_planner_prompt(
            "05_icp_step_planner",
            context_json=json.dumps(context_for_prompt, ensure_ascii=False, indent=2),
            planning_state=context_for_prompt["planning_state"]
        )

        # 3. 调用轻量级LLM进行思考
        # 遵循分层模型策略，使用'basic'模型进行高频次的战术决策
        llm_manager = LLMManager()
        basic_llm_client = llm_manager.get_client("basic")
        
        response = await basic_llm_client.chat(formatted_prompt)
        response_text = response['content']

        # 4. 解析LLM返回的JSON
        # 移除Markdown代码块标记
        cleaned_json = response_text.strip().replace("```json", "").replace("```", "").strip()
        decision = json.loads(cleaned_json)
        
        logger.info(f"LLM a \n\n{decision.get('thought')}")

        return decision

    except Exception as e:
        logger.error(f"LLM驱动的思考生成失败: {str(e)}")
        # 返回一个安全的、默认的结束动作
        return {
            "thought": f"思考过程出现严重错误: {e}。为避免卡死，决定结束今天的规划。",
            "action": {
                "tool_name": "end_day_planning",
                "parameters": {}
            },
            "estimated_duration_minutes": 0
        }


@unified_registry.register_action_tool
def select_poi_from_pool(poi_name: str, current_state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从剩余POI池中按名称选择一个POI，并从池中移除以防重复选择。

    Args:
        poi_name: 要选择的POI的名称。
        current_state: 当前规划状态，包含remaining_pois。

    Returns:
        找到的POI字典，如果未找到则返回None。
    """
    remaining_pois = current_state.get("remaining_pois", [])
    used_poi_ids = current_state.get("used_poi_ids", [])

    for i, poi in enumerate(remaining_pois):
        if poi.get("name") == poi_name:
            # 检查是否已被使用
            poi_id = poi.get("id", poi.get("poi_id", ""))
            if poi_id in used_poi_ids:
                logger.warning(f"POI '{poi_name}' (ID: {poi_id}) 已被使用，跳过")
                continue

            # 从剩余池中移除并标记为已使用
            selected_poi = remaining_pois.pop(i)
            if poi_id:
                used_poi_ids.append(poi_id)

            logger.info(f"成功从POI池中选择了 '{poi_name}' (ID: {poi_id})，剩余POI: {len(remaining_pois)}个")
            return selected_poi

    logger.warning(f"无法在POI池中找到名为 '{poi_name}' 的POI")
    return None


@unified_registry.register_planner_tool
def select_next_action(
    thought_result: Dict[str, Any],
    available_tools: List[str],
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    选择下一步行动
    
    基于思考结果和可用工具，选择最合适的下一步行动
    
    Args:
        thought_result: 思考结果
        available_tools: 可用工具列表
        current_state: 当前状态
    
    Returns:
        行动选择结果
    """
    try:
        suggested_action = thought_result.get("next_action_suggestion", {})
        action_type = suggested_action.get("action_type", "search_poi")
        
        # 根据行动类型选择具体工具和参数
        if action_type == "search_poi":
            # 确定搜索参数
            daily_plans = current_state.get("daily_plans", {})
            current_day = len([day for day, plans in daily_plans.items() if plans]) + 1
            
            # 从consolidated_intent获取目的地信息
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            current_destination = destinations[0] if destinations else "北京"
            
            # 从偏好分析获取景点类型
            preferences = consolidated_intent.get("preferences", {})
            attraction_prefs = preferences.get("attractions", {})
            preferred_types = attraction_prefs.get("preferred_types", ["历史文化"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": preferred_types[0] if preferred_types else "景点",
                    "city": current_destination,
                    "types": "景点",
                    "page_size": 10
                },
                "target_day": current_day,
                "expected_result": f"为第{current_day}天获取{preferred_types[0] if preferred_types else '景点'}列表"
            }
            
        elif action_type == "search_accommodation":
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "酒店",
                    "city": destinations[0] if destinations else "北京",
                    "types": "住宿",
                    "page_size": 5
                },
                "expected_result": "获取住宿选项列表"
            }
            
        elif action_type == "finalize_itinerary":
            action = {
                "tool_name": "format_final_itinerary",
                "parameters": {
                    "daily_plans": current_state.get("daily_plans", {}),
                    "consolidated_intent": current_state.get("consolidated_intent", {})
                },
                "expected_result": "生成最终完整行程"
            }
            
        else:
            # 默认行动
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "景点",
                    "city": "北京",
                    "page_size": 5
                },
                "expected_result": "获取景点信息"
            }
        
        return {
            "selected_action": action,
            "action_reasoning": suggested_action.get("reason", "基于当前状态选择的行动"),
            "confidence": thought_result.get("confidence", 0.8),
            "alternatives": []  # 可以添加备选行动
        }
        
    except Exception as e:
        logger.error(f"Failed to select next action: {str(e)}")
        return {
            "selected_action": {
                "tool_name": "search_poi",
                "parameters": {"keywords": "景点", "city": "北京"},
                "expected_result": "获取基础景点信息"
            },
            "action_reasoning": f"行动选择失败，使用默认行动: {str(e)}",
            "confidence": 0.1,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def observe_action_result(
    action: Dict[str, Any],
    action_result: Any,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    观察行动结果
    
    分析工具执行结果，评估是否达到预期目标
    
    Args:
        action: 执行的行动
        action_result: 行动执行结果
        current_state: 当前状态
    
    Returns:
        观察结果
    """
    try:
        tool_name = action.get("tool_name", "unknown")
        expected_result = action.get("expected_result", "")
        
        # 分析结果质量
        if action_result is None:
            observation = {
                "success": False,
                "quality_score": 0.0,
                "observation": "工具执行失败，未获得结果",
                "next_step_suggestion": "重试或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) == 0:
            observation = {
                "success": False,
                "quality_score": 0.2,
                "observation": "工具执行成功但未找到相关结果",
                "next_step_suggestion": "调整搜索参数或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) > 0:
            # 分析POI搜索结果
            quality_score = min(1.0, len(action_result) / 5.0)  # 5个结果为满分
            
            observation = {
                "success": True,
                "quality_score": quality_score,
                "observation": f"成功获得{len(action_result)}个结果，质量评分{quality_score:.2f}",
                "result_summary": {
                    "count": len(action_result),
                    "sample_items": [item.get("name", "未知") for item in action_result[:3]]
                },
                "next_step_suggestion": "将结果添加到行程规划中" if quality_score > 0.6 else "考虑调整搜索条件"
            }
        else:
            # 其他类型结果
            observation = {
                "success": True,
                "quality_score": 0.7,
                "observation": f"获得{tool_name}执行结果",
                "result_summary": str(action_result)[:200],
                "next_step_suggestion": "继续下一步规划"
            }
        
        # 评估是否需要继续规划
        daily_plans = current_state.get("daily_plans", {})
        total_days = current_state.get("consolidated_intent", {}).get("travel_days", 3)
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        
        should_continue = completed_days < total_days or not observation["success"]
        
        observation.update({
            "should_continue_planning": should_continue,
            "planning_progress": {
                "completed_days": completed_days,
                "total_days": total_days,
                "completion_rate": completed_days / total_days if total_days > 0 else 0
            }
        })
        
        return observation
        
    except Exception as e:
        logger.error(f"Failed to observe action result: {str(e)}")
        return {
            "success": False,
            "quality_score": 0.0,
            "observation": f"结果观察失败: {str(e)}",
            "should_continue_planning": True,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def update_planning_state(
    current_state: Dict[str, Any],
    action: Dict[str, Any],
    action_result: Any,
    observation: Dict[str, Any]
) -> Dict[str, Any]:
    """
    更新规划状态
    
    基于行动结果和观察，更新规划状态
    
    Args:
        current_state: 当前状态
        action: 执行的行动
        action_result: 行动结果
        observation: 观察结果
    
    Returns:
        更新后的状态
    """
    try:
        updated_state = current_state.copy()
        
        # 更新工具结果缓存
        tool_results = updated_state.get("tool_results", {})
        tool_name = action.get("tool_name", "unknown")
        tool_results[tool_name] = action_result
        updated_state["tool_results"] = tool_results
        
        # 如果是POI搜索且成功，更新daily_plans（带主POI池管理和去重逻辑）
        if tool_name == "search_poi" and observation.get("success", False):
            target_day = action.get("target_day")
            if target_day and isinstance(action_result, list):
                daily_plans = updated_state.get("daily_plans", {})
                remaining_pois = updated_state.get("remaining_pois", [])
                used_poi_ids = set(updated_state.get("used_poi_ids", []))

                # 获取当前已有的POI列表
                existing_pois = daily_plans.get(target_day, [])
                existing_poi_ids = {poi.get("poi_id", "") for poi in existing_pois}
                existing_poi_names = {poi.get("name", "").lower() for poi in existing_pois}

                # 智能POI选择策略：优先从主POI池中选择，实现类型平衡
                new_pois = []
                pois_to_remove = []  # 需要从remaining_pois中移除的POI

                # 分析当前天已有的POI类型
                existing_poi_types = {}
                for existing_poi in existing_pois:
                    poi_type = existing_poi.get("poi_type", "ATTRACTION")
                    existing_poi_types[poi_type] = existing_poi_types.get(poi_type, 0) + 1

                # 如果有剩余POI池，优先从中智能选择
                if remaining_pois:
                    logger.info(f"从剩余POI池中智能选择POI，池中还有{len(remaining_pois)}个POI")
                    # 获取当前位置信息用于距离计算
                    current_location = updated_state.get("icp_planner_state", {}).get("current_location", {})
                    # 实现POI类型平衡选择，考虑距离因素
                    available_pois = _select_balanced_pois(remaining_pois, existing_poi_types, 5, current_location)
                    logger.info(f"智能选择了{len(available_pois)}个平衡的POI")
                else:
                    logger.info(f"剩余POI池为空，使用搜索结果")
                    available_pois = action_result[:5]  # 从搜索结果中取前5个

                for poi in available_pois:
                    # 使用真实的POI ID，而不是生成的序号
                    real_poi_id = poi.get("id", "")  # 使用高德API返回的真实ID
                    poi_name = poi.get("name", "未知景点")
                    poi_name_lower = poi_name.lower()

                    # 检查是否已被使用（通过真实ID和名称双重检查）
                    if (real_poi_id and real_poi_id in used_poi_ids) or (real_poi_id and real_poi_id in existing_poi_ids) or (poi_name_lower in existing_poi_names):
                        logger.info(f"跳过已使用的POI: {poi_name} (真实ID: {real_poi_id})")
                        continue

                    # 如果没有真实ID，生成一个唯一ID（包含名称和地址的哈希）
                    if not real_poi_id:
                        import hashlib
                        unique_str = f"{poi_name}_{poi.get('address', '')}_{poi.get('location', '')}"
                        real_poi_id = hashlib.md5(unique_str.encode()).hexdigest()[:12]
                        logger.info(f"为POI生成唯一ID: {poi_name} -> {real_poi_id}")

                    # 创建新POI条目
                    new_poi = {
                        "poi_id": real_poi_id,
                        "name": poi_name,
                        "address": poi.get("address", ""),
                        "poi_type": _determine_poi_type(poi.get("typecode", ""), poi.get("type", "")),
                        "location": poi.get("location", ""),
                        "rating": poi.get("rating", 0),
                        "phone_number": poi.get("tel", ""),
                        "introduction": poi.get("description", f"{poi_name}是一个值得游览的地方"),
                        "suggested_time": _generate_suggested_time(_determine_poi_type(poi.get("typecode", ""), poi.get("type", ""))),
                        "image_urls": poi.get("photos", [])
                    }

                    new_pois.append(new_poi)
                    existing_poi_names.add(poi_name_lower)
                    existing_poi_ids.add(new_poi["poi_id"])

                    # 标记为已使用
                    if real_poi_id:
                        used_poi_ids.add(real_poi_id)
                        # 找到对应的POI并标记为需要移除
                        for remaining_poi in remaining_pois:
                            if remaining_poi.get("id") == real_poi_id:
                                pois_to_remove.append(remaining_poi)
                                break

                    # 限制每天最多3个新POI
                    if len(new_pois) >= 3:
                        break

                # 从remaining_pois中移除已使用的POI
                for poi_to_remove in pois_to_remove:
                    if poi_to_remove in remaining_pois:
                        remaining_pois.remove(poi_to_remove)
                        logger.info(f"从剩余POI池中移除: {poi_to_remove.get('name', 'Unknown')}")

                # 合并现有POI和新POI
                if new_pois:
                    daily_plans[target_day] = existing_pois + new_pois
                    updated_state["daily_plans"] = daily_plans
                    updated_state["remaining_pois"] = remaining_pois
                    updated_state["used_poi_ids"] = list(used_poi_ids)  # 转换为list以支持JSON序列化
                    logger.info(f"为第{target_day}天添加了{len(new_pois)}个新POI，总计{len(daily_plans[target_day])}个POI")
                    logger.info(f"剩余POI池还有{len(remaining_pois)}个POI，已使用{len(used_poi_ids)}个POI")
                else:
                    logger.warning(f"第{target_day}天没有找到新的POI，可能都已存在或POI池已耗尽")
        
        # 更新规划日志
        planning_log = updated_state.get("planning_log", [])
        log_entry = f"执行{tool_name}，结果：{observation.get('observation', '未知')}"
        planning_log.append(log_entry)
        updated_state["planning_log"] = planning_log
        
        # 更新当前行动
        updated_state["current_action"] = action
        
        return updated_state
        
    except Exception as e:
        logger.error(f"Failed to update planning state: {str(e)}")
        return current_state


@unified_registry.register_planner_tool
def check_planning_completion(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    检查规划完成情况
    
    评估当前规划是否已完成或需要继续
    
    Args:
        current_state: 当前状态
        planning_context: 规划上下文
    
    Returns:
        完成情况检查结果
    """
    try:
        daily_plans = current_state.get("daily_plans", {})
        total_days = planning_context.get("constraints", {}).get("max_days", 3)
        
        # 检查每天是否都有规划
        completed_days = 0
        for day in range(1, total_days + 1):
            if day in daily_plans and daily_plans[day]:
                completed_days += 1
        
        completion_rate = completed_days / total_days if total_days > 0 else 0
        is_complete = completion_rate >= 1.0
        
        # 检查规划质量
        quality_issues = []
        if completed_days < total_days:
            quality_issues.append(f"还有{total_days - completed_days}天未规划")
        
        # 检查预算
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        if budget_used > budget_limit:
            quality_issues.append(f"预算超支{budget_used - budget_limit}元")
        
        return {
            "is_complete": is_complete,
            "completion_rate": completion_rate,
            "completed_days": completed_days,
            "total_days": total_days,
            "quality_score": 1.0 - len(quality_issues) * 0.2,
            "quality_issues": quality_issues,
            "recommendation": "规划完成" if is_complete and not quality_issues else "需要继续规划或优化"
        }
        
    except Exception as e:
        logger.error(f"Failed to check planning completion: {str(e)}")
        return {
            "is_complete": False,
            "completion_rate": 0.0,
            "quality_score": 0.0,
            "error": str(e),
            "recommendation": "检查失败，建议重新评估"
        }


@unified_registry.register_action_tool
async def optimize_daily_route(pois: List[Dict[str, Any]], trace_id: str = "") -> List[Dict[str, Any]]:
    """
    优化当天的POI访问顺序，基于地理位置进行排序以减少路线上的"反复横跳"

    Args:
        pois: POI列表，每个POI应包含location字段（格式为"lng,lat"）
        trace_id: 追踪ID

    Returns:
        优化后的POI列表
    """
    try:
        logger.info(f"[{trace_id}] 开始优化每日路线，POI数量: {len(pois)}")

        if len(pois) <= 1:
            logger.info(f"[{trace_id}] POI数量不足，无需优化")
            return pois

        # 提取有效位置的POI
        pois_with_location = []
        pois_without_location = []

        for poi in pois:
            location = poi.get('location', '').strip()
            if location and ',' in location:
                try:
                    lng_str, lat_str = location.split(',', 1)
                    lng, lat = float(lng_str.strip()), float(lat_str.strip())

                    # 验证经纬度范围
                    if -180 <= lng <= 180 and -90 <= lat <= 90:
                        poi_copy = poi.copy()
                        poi_copy['_lng'] = lng
                        poi_copy['_lat'] = lat
                        pois_with_location.append(poi_copy)
                    else:
                        logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 经纬度超出范围: {location}")
                        pois_without_location.append(poi)
                except (ValueError, IndexError) as e:
                    logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 位置解析失败: {location}, 错误: {str(e)}")
                    pois_without_location.append(poi)
            else:
                logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 缺少有效位置信息")
                pois_without_location.append(poi)

        if not pois_with_location:
            logger.warning(f"[{trace_id}] 没有有效位置的POI，返回原始顺序")
            return pois

        # 实现简单的地理排序算法
        # 按经度排序，然后按纬度排序，实现从西到东、从南到北的游览顺序
        pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

        # 移除临时添加的经纬度字段
        for poi in pois_with_location:
            poi.pop('_lng', None)
            poi.pop('_lat', None)

        # 将有位置的POI和无位置的POI合并（无位置的放在最后）
        optimized_pois = pois_with_location + pois_without_location

        logger.info(f"[{trace_id}] 路线优化完成，有效位置POI: {len(pois_with_location)}, 无位置POI: {len(pois_without_location)}")

        return optimized_pois

    except Exception as e:
        logger.error(f"[{trace_id}] 路线优化失败: {str(e)}")
        return pois  # 返回原始顺序


@unified_registry.register_action_tool
async def calculate_route_distance(pois: List[Dict[str, Any]], trace_id: str = "") -> Dict[str, Any]:
    """
    计算POI列表的总路线距离和时间

    Args:
        pois: POI列表
        trace_id: 追踪ID

    Returns:
        路线统计信息
    """
    try:
        logger.info(f"[{trace_id}] 开始计算路线距离，POI数量: {len(pois)}")

        if len(pois) < 2:
            return {
                "total_distance": 0.0,
                "total_duration": 0,
                "route_segments": [],
                "average_distance": 0.0
            }

        total_distance = 0.0  # 公里
        total_duration = 0    # 分钟
        route_segments = []

        for i in range(len(pois) - 1):
            current_poi = pois[i]
            next_poi = pois[i + 1]

            current_location = current_poi.get('location', '')
            next_location = next_poi.get('location', '')

            if current_location and next_location:
                # 计算直线距离（简化版本）
                distance = _calculate_haversine_distance(current_location, next_location)
                # 估算步行时间（假设步行速度4km/h）
                duration = int(distance * 15)  # 分钟

                segment = {
                    "from": current_poi.get('name', 'Unknown'),
                    "to": next_poi.get('name', 'Unknown'),
                    "distance": round(distance, 2),
                    "duration": duration
                }
                route_segments.append(segment)

                total_distance += distance
                total_duration += duration

        average_distance = total_distance / len(route_segments) if route_segments else 0.0

        result = {
            "total_distance": round(total_distance, 2),
            "total_duration": total_duration,
            "route_segments": route_segments,
            "average_distance": round(average_distance, 2)
        }

        logger.info(f"[{trace_id}] 路线计算完成，总距离: {result['total_distance']}km, 总时间: {result['total_duration']}分钟")

        return result

    except Exception as e:
        logger.error(f"[{trace_id}] 路线距离计算失败: {str(e)}")
        return {
            "total_distance": 0.0,
            "total_duration": 0,
            "route_segments": [],
            "average_distance": 0.0
        }


def _calculate_haversine_distance(location1: str, location2: str) -> float:
    """
    使用Haversine公式计算两个经纬度点之间的直线距离

    Args:
        location1: 位置1，格式为"lng,lat"
        location2: 位置2，格式为"lng,lat"

    Returns:
        距离（公里）
    """
    if not location1 or not location2 or "," not in location1 or "," not in location2:
        return float('inf') # 返回无穷大，使无效位置的POI排序在最后

    try:
        lng1, lat1 = map(float, location1.split(','))
        lng2, lat2 = map(float, location2.split(','))

        # 转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（公里）
        r = 6371

        return c * r

    except (ValueError, IndexError):
        return 0.0


def _determine_poi_type(typecode: str, type_name: str) -> str:
    """
    根据高德API的typecode和type字段确定POI类型

    Args:
        typecode: 高德API返回的类型编码
        type_name: 高德API返回的类型名称

    Returns:
        标准化的POI类型: ATTRACTION, RESTAURANT, HOTEL
    """
    # 优先根据type_name判断，更准确
    if type_name:
        type_lower = type_name.lower()
        if any(keyword in type_lower for keyword in ["餐厅", "饭店", "美食", "小吃", "咖啡", "茶楼", "店", "烤鸭"]):
            return "restaurant"
        if any(keyword in type_lower for keyword in ["酒店", "宾馆", "旅馆", "客栈", "民宿"]):
            return "hotel"
        if any(keyword in type_lower for keyword in ["景点", "公园", "博物馆", "寺庙", "古迹", "广场", "坛", "门", "历史"]):
            return "scenic"

    # 如果type_name不明确，再根据typecode判断
    if typecode:
        # 餐饮服务类型编码以05开头
        if typecode.startswith("05"):
            return "restaurant"
        # 住宿服务类型编码以10开头
        elif typecode.startswith("10"):
            return "hotel"
        # 风景名胜类型编码以11开头
        elif typecode.startswith("11"):
            return "scenic"

    # 默认返回景点类型
    return "scenic"


def _select_balanced_pois(remaining_pois: list, existing_poi_types: dict, max_count: int, current_location: dict = None) -> list:
    """
    从剩余POI池中智能选择平衡的POI，考虑距离和类型平衡

    Args:
        remaining_pois: 剩余POI池
        existing_poi_types: 当前已有的POI类型统计
        max_count: 最大选择数量
        current_location: 当前位置（用于距离计算）

    Returns:
        选择的POI列表
    """
    import random
    import math

    if not remaining_pois:
        return []

    # 按POI类型分组
    pois_by_type = {
        "ATTRACTION": [],
        "RESTAURANT": [],
        "HOTEL": []
    }

    for poi in remaining_pois:
        poi_type = _determine_poi_type(poi.get("typecode", ""), poi.get("type", ""))
        if poi_type in pois_by_type:
            pois_by_type[poi_type].append(poi)

    selected_pois = []

    # 优先选择缺少的类型
    attraction_count = existing_poi_types.get("ATTRACTION", 0)
    restaurant_count = existing_poi_types.get("RESTAURANT", 0)

    # 每天理想的POI组合：2个景点 + 1个餐厅
    target_attractions = max(0, 2 - attraction_count)
    target_restaurants = max(0, 1 - restaurant_count)

    # 距离感知选择函数
    def select_by_distance_and_rating(poi_list: list, count: int) -> list:
        if not poi_list or count <= 0:
            return []

        # 如果有当前位置，按距离排序；否则按评分排序
        if current_location and current_location.get("lon") and current_location.get("lat"):
            current_loc_str = f"{current_location['lon']},{current_location['lat']}"
            
            # 计算距离并排序
            poi_with_distance = []
            for poi in poi_list:
                distance = _calculate_haversine_distance(current_loc_str, poi.get("location"))
                rating = float(poi.get("rating", 0))

                # 综合评分：距离越近越好（距离无穷大时评分为负无穷），评分越高越好
                # 避免除以零
                score = rating - (distance / 10 if distance > 0 else 0)
                poi_with_distance.append((poi, score))

            # 按综合评分排序
            poi_with_distance.sort(key=lambda x: x[1], reverse=True)
            return [poi for poi, _ in poi_with_distance[:count]]
        else:
            # 按评分排序
            sorted_pois = sorted(poi_list, key=lambda x: float(x.get("rating", 0)), reverse=True)
            return sorted_pois[:count]

    # 智能选择景点（优先距离近且评分高的）
    if target_attractions > 0 and pois_by_type["ATTRACTION"]:
        attractions = select_by_distance_and_rating(pois_by_type["ATTRACTION"], target_attractions)
        selected_pois.extend(attractions)

    # 智能选择餐厅
    if target_restaurants > 0 and pois_by_type["RESTAURANT"]:
        restaurants = select_by_distance_and_rating(pois_by_type["RESTAURANT"], target_restaurants)
        selected_pois.extend(restaurants)

    # 如果还没达到最大数量，智能选择剩余的POI
    if len(selected_pois) < max_count:
        remaining_count = max_count - len(selected_pois)
        used_ids = {poi.get("id") for poi in selected_pois}

        available_remaining = [
            poi for poi in remaining_pois
            if poi.get("id") not in used_ids
        ]

        if available_remaining:
            additional_pois = select_by_distance_and_rating(available_remaining, remaining_count)
            selected_pois.extend(additional_pois)

    logger.info(f"智能POI选择完成：选择了{len(selected_pois)}个POI，类型分布：{[poi.get('poi_type', 'UNKNOWN') for poi in selected_pois]}")
    return selected_pois[:max_count]


# ==================== P1阶段：原子化时空工具 ====================

@unified_registry.register_planner_tool
async def get_travel_time_and_distance(
    origin: Dict[str, Any],
    destination: Dict[str, Any]
) -> Dict[str, Any]:
    """
    计算任意两点间的驾驶时间和距离

    Args:
        origin: 起点位置信息 {"name": str, "lat": float, "lon": float}
        destination: 终点位置信息 {"name": str, "lat": float, "lon": float}

    Returns:
        距离和时间信息 {"duration_minutes": int, "distance_km": float}
    """
    try:
        logger.info(f"计算路线 - 起点: {origin.get('name')}, 终点: {destination.get('name')}")

        # 获取全局AmapService实例
        from src.agents.services.amap_service import _get_global_amap_service
        amap_service = _get_global_amap_service()

        # 优先使用经纬度信息进行路线规划
        if origin.get('lon') and origin.get('lat'):
            origin_str = f"{origin['lon']},{origin['lat']}"
        else:
            origin_str = f"{origin.get('name', '')}"
            
        if destination.get('lon') and destination.get('lat'):
            destination_str = f"{destination['lon']},{destination['lat']}"
        else:
            destination_str = f"{destination.get('name', '')}"

        # 调用高德路线规划API
        route_result = await amap_service.get_driving_route(
            origin=origin_str,
            destination=destination_str,
            strategy=0  # 速度优先
        )

        if route_result:
            distance_meters = route_result.get("distance", 0)
            duration_seconds = route_result.get("duration", 0)

            result = {
                "duration_minutes": max(1, duration_seconds // 60),  # 至少1分钟
                "distance_km": round(distance_meters / 1000, 2),
                "origin": origin.get('name', '起点'),
                "destination": destination.get('name', '终点')
            }

            logger.info(f"路线计算成功 - 距离: {result['distance_km']}km, 时间: {result['duration_minutes']}分钟")
            return result
        else:
            # 如果路线规划失败，使用直线距离估算
            logger.warning(f"路线规划失败，使用直线距离估算")
            distance_km = _calculate_haversine_distance(
                f"{origin.get('lon', 0)},{origin.get('lat', 0)}",
                f"{destination.get('lon', 0)},{destination.get('lat', 0)}"
            )
            # 估算驾车时间（假设平均速度40km/h）
            duration_minutes = max(1, int(distance_km / 40 * 60))

            return {
                "duration_minutes": duration_minutes,
                "distance_km": distance_km,
                "origin": origin.get('name', '起点'),
                "destination": destination.get('name', '终点'),
                "estimated": True
            }

    except Exception as e:
        logger.error(f"路线计算失败: {str(e)}")
        # 返回默认值避免卡死
        return {
            "duration_minutes": 30,  # 默认30分钟
            "distance_km": 10.0,     # 默认10公里
            "origin": origin.get('name', '起点'),
            "destination": destination.get('name', '终点'),
            "error": str(e)
        }


@unified_registry.register_planner_tool
def calculate_nearby_pois_sorted_by_distance(
    current_location: Dict[str, Any],
    remaining_pois: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    实现动态位置感知的核心计算逻辑

    Args:
        current_location: 当前位置 {"name": str, "lat": float, "lon": float}
        remaining_pois: 剩余POI池

    Returns:
        按距离排序并附加距离信息的POI列表
    """
    try:
        logger.info(f"计算附近POI - 当前位置: {current_location.get('name')}, 剩余POI数: {len(remaining_pois)}")

        if not remaining_pois:
            logger.info("剩余POI池为空")
            return []

        current_loc_str = f"{current_location.get('lon', 0)},{current_location.get('lat', 0)}"
        nearby_pois = []

        for poi in remaining_pois:
            poi_copy = poi.copy()  # 创建副本避免修改原始数据

            # 获取POI位置
            poi_location = poi.get("location", "")
            if poi_location and "," in poi_location:
                # 计算直线距离
                distance_km = _calculate_haversine_distance(current_loc_str, poi_location)
                poi_copy["distance_km"] = round(distance_km, 2)
            else:
                # 如果没有位置信息，设置为无穷大距离
                poi_copy["distance_km"] = float('inf')
                logger.warning(f"POI {poi.get('name', 'Unknown')} 缺少位置信息")

            nearby_pois.append(poi_copy)

        # 按距离升序排序
        nearby_pois.sort(key=lambda x: x.get("distance_km", float('inf')))

        # 简化日志输出，避免复杂的f-string嵌套
        top_5_names = [poi.get('name', 'Unknown') for poi in nearby_pois[:5]]
        logger.info(f"附近POI计算完成 - 最近的5个: {top_5_names}")

        return nearby_pois

    except Exception as e:
        logger.error(f"附近POI计算失败: {str(e)}")
        # 返回原始列表避免卡死
        return remaining_pois


@unified_registry.register_planner_tool
async def schedule_activity(
    poi: Dict[str, Any],
    activity_duration_minutes: int,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    原子化状态更新工具 - 驱动时空状态更新的核心原子工具

    Args:
        poi: 已选定的POI对象
        activity_duration_minutes: 活动时长（分钟）
        current_state: 当前规划状态

    Returns:
        更新结果和新的活动信息
    """
    try:
        logger.info(f"调度活动 - POI: {poi.get('name')}, 时长: {activity_duration_minutes}分钟")

        # 获取当前时空状态
        icp_state = current_state.get("icp_planner_state", {})
        current_time = icp_state.get("current_time", "09:00")
        current_location = icp_state.get("current_location", {})
        current_day = icp_state.get("current_day", 1)

        # 准备目标位置
        poi_location = poi.get("location", "")
        target_location = {
            "name": poi.get("name", "未知地点"),
            "lat": float(poi_location.split(",")[1]) if "," in poi_location else 0,
            "lon": float(poi_location.split(",")[0]) if "," in poi_location else 0
        }

        # 步骤1: 计算交通时间
        travel_info = await get_travel_time_and_distance(current_location, target_location)
        travel_minutes = travel_info.get("duration_minutes", 30)

        # 步骤2: 计算活动时间
        start_time = _advance_time(current_time, travel_minutes)
        end_time = _advance_time(start_time, activity_duration_minutes)

        # 步骤3: 构建完整的活动对象
        activity = {
            "activity_type": poi.get("poi_type", _determine_poi_type(poi.get("typecode", ""), poi.get("type", ""))),
            "start_time": start_time,
            "end_time": end_time,
            "duration_minutes": activity_duration_minutes,
            "transport_to": {
                "duration_minutes": travel_minutes,
                "distance_km": travel_info.get("distance_km", 0),
                "from": current_location.get("name", "起点"),
                "to": target_location["name"]
            },
            "poi_details": poi, # 将完整的POI信息作为子字典
            "scheduled_at": datetime.now().isoformat()
        }

        # 步骤4: 添加到结构化行程中
        structured_itinerary = current_state.get("structured_itinerary", {})
        if current_day not in structured_itinerary:
            structured_itinerary[current_day] = []
        structured_itinerary[current_day].append(activity)

        # 步骤5: 原子化更新时空状态
        new_current_time = _advance_time(current_time, travel_minutes + activity_duration_minutes)
        
        # 更新icp_planner_state
        icp_state.update({
            "current_time": new_current_time,
            "current_location": target_location
        })

        logger.info(f"活动调度完成 - {poi.get('name')}: {start_time}-{end_time}, 下个位置时间: {new_current_time}")

        return {
            "success": True,
            "activity": activity,
            "new_current_time": new_current_time,
            "new_current_location": target_location,
            "travel_info": travel_info,
            "updated_itinerary": structured_itinerary
        }

    except Exception as e:
        logger.error(f"活动调度失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "poi_name": poi.get("name", "未知")
        }


@unified_registry.register_action_tool
async def search_poi_by_name(
    poi_name: str,
    city: str,
    task_id: str = None
) -> Optional[Dict[str, Any]]:
    """
    按名称搜索特定POI - 赋予AI动态搜索能力

    Args:
        poi_name: 要搜索的POI名称
        city: 所在城市
        task_id: 任务ID（可选）

    Returns:
        找到的POI信息，如果未找到则返回None
    """
    try:
        logger.info(f"按名称搜索POI - 名称: {poi_name}, 城市: {city}")

        # 获取全局AmapService实例
        from src.agents.services.amap_service import _get_global_amap_service
        amap_service = _get_global_amap_service()

        # 调用底层搜索服务
        search_results = await amap_service.search_poi(
            keywords=poi_name,
            city=city,
            page_size=5
        )

        if search_results and len(search_results) > 0:
            # 选择最相关的结果（通常是第一个）
            best_match = search_results[0]
            
            # 格式化为标准POI结构
            poi_result = {
                "id": best_match.get("id", ""),
                "name": best_match.get("name", poi_name),
                "address": best_match.get("address", ""),
                "location": best_match.get("location", ""),
                "poi_type": _determine_poi_type(best_match.get("typecode", ""), best_match.get("type", "")),
                "rating": float(best_match.get("rating", 0)) if best_match.get("rating") else 0,
                "phone_number": best_match.get("tel", ""),
                "photos": best_match.get("photos", []),
                "business_area": best_match.get("business_area", ""),
                "typecode": best_match.get("typecode", ""),
                "type": best_match.get("type", ""),
                "search_source": "dynamic_search"
            }

            logger.info(f"POI搜索成功 - 找到: {poi_result['name']} at {poi_result['address']}")
            return poi_result
        else:
            logger.warning(f"POI搜索失败 - 未找到: {poi_name} in {city}")
            return None

    except Exception as e:
        logger.error(f"POI搜索异常 - 名称: {poi_name}, 错误: {str(e)}")
        return None


@unified_registry.register_planner_tool
async def search_poi(
    keywords: str,
    city: str,
    current_location: Optional[Dict[str, Any]] = None,
    page_size: int = 5
) -> Dict[str, Any]:
    """
    搜索POI工具 - 支持动态搜索新的POI

    Args:
        keywords: 搜索关键词
        city: 所在城市
        current_location: 当前位置（可选）
        page_size: 返回数量

    Returns:
        搜索结果 {"success": bool, "pois": List[Dict], "error": str}
    """
    try:
        logger.info(f"搜索POI - 关键词: {keywords}, 城市: {city}, 数量: {page_size}")

        # 获取全局AmapService实例
        from src.agents.services.amap_service import _get_global_amap_service
        amap_service = _get_global_amap_service()

        # 调用AmapService进行搜索
        search_results = await amap_service.search_poi(
            keywords=keywords,
            city=city,
            page_size=page_size
        )

        if search_results:
            # 格式化搜索结果，确保兼容后续处理
            formatted_pois = []
            for poi in search_results:
                formatted_poi = {
                    "id": poi.get("id", ""),
                    "poi_id": poi.get("id", ""),  # 确保有poi_id字段
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "location": poi.get("location", ""),
                    "poi_type": _determine_poi_type(poi.get("typecode", ""), poi.get("type", "")),
                    "rating": float(poi.get("rating", 0)) if poi.get("rating") else 0,
                    "phone_number": poi.get("tel", ""),
                    "photos": poi.get("photos", []),
                    "business_hours": poi.get("business_hours", "未知"),
                    "typecode": poi.get("typecode", ""),
                    "type": poi.get("type", ""),
                    "tags": poi.get("type", "").split(';') if poi.get("type") else []
                }
                formatted_pois.append(formatted_poi)

            logger.info(f"POI搜索成功 - 找到 {len(formatted_pois)} 个结果")
            return {
                "success": True,
                "pois": formatted_pois,
                "total_count": len(formatted_pois)
            }
        else:
            logger.warning(f"POI搜索未找到结果 - 关键词: {keywords}, 城市: {city}")
            return {
                "success": False,
                "pois": [],
                "error": "未找到相关POI"
            }

    except Exception as e:
        logger.error(f"POI搜索异常 - 关键词: {keywords}, 错误: {str(e)}")
        return {
            "success": False,
            "pois": [],
            "error": str(e)
        }


def _advance_time(current_time: str, minutes_to_add: int) -> str:
    """
    推进时间的辅助函数

    Args:
        current_time: 当前时间字符串 (如 "09:30")
        minutes_to_add: 要增加的分钟数

    Returns:
        推进后的时间字符串
    """
    try:
        # 解析当前时间
        hour, minute = map(int, current_time.split(":"))
        
        # 转换为总分钟数
        total_minutes = hour * 60 + minute + minutes_to_add
        
        # 转换回小时和分钟
        new_hour = (total_minutes // 60) % 24  # 处理跨天情况
        new_minute = total_minutes % 60
        
        return f"{new_hour:02d}:{new_minute:02d}"
    
    except Exception as e:
        logger.warning(f"时间推进失败: {current_time} + {minutes_to_add}, 错误: {str(e)}")
        return current_time


def _check_meal_interval(daily_activities: list, current_time: str) -> dict:
    """
    检查用餐间隔，防止重复用餐

    Args:
        daily_activities: 当天已安排的活动
        current_time: 当前时间

    Returns:
        dict: 用餐间隔检查结果
    """
    try:
        from datetime import datetime

        # 解析当前时间
        current_obj = datetime.strptime(current_time, "%H:%M")
        current_minutes = current_obj.hour * 60 + current_obj.minute

        # 查找最近的用餐活动
        latest_meal = None
        latest_meal_end_minutes = 0

        for activity in daily_activities:
            # 检查是否为餐厅活动
            poi_type = activity.get("poi_type", "").lower()
            activity_type = activity.get("activity_type", "").lower()
            poi_details = activity.get("poi_details", {})
            poi_details_type = poi_details.get("poi_type", "").lower() if poi_details else ""

            is_restaurant = (
                poi_type == "restaurant" or
                activity_type == "restaurant" or
                poi_details_type == "restaurant"
            )

            if is_restaurant:
                end_time = activity.get("end_time")
                if end_time:
                    try:
                        end_obj = datetime.strptime(end_time, "%H:%M")
                        end_minutes = end_obj.hour * 60 + end_obj.minute

                        # 找到最近的用餐结束时间
                        if end_minutes > latest_meal_end_minutes:
                            latest_meal_end_minutes = end_minutes
                            latest_meal = {
                                "name": poi_details.get("name") or activity.get("poi_name", "未知餐厅"),
                                "end_time": end_time,
                                "end_minutes": end_minutes
                            }
                    except:
                        continue

        # 计算距离最近用餐的时间间隔
        if latest_meal:
            interval_minutes = current_minutes - latest_meal_end_minutes
            interval_hours = interval_minutes / 60

            # 判断是否可以用餐（至少间隔2小时）
            can_eat = interval_minutes >= 120  # 120分钟 = 2小时

            result = {
                "has_recent_meal": True,
                "latest_meal": latest_meal,
                "interval_minutes": interval_minutes,
                "interval_hours": round(interval_hours, 1),
                "can_eat": can_eat,
                "reason": f"距离上次用餐({latest_meal['name']}, {latest_meal['end_time']})已过{interval_minutes}分钟" if can_eat else f"距离上次用餐({latest_meal['name']}, {latest_meal['end_time']})仅过{interval_minutes}分钟，不足2小时"
            }

            print(f"[调试] 用餐间隔检查: {result['reason']}, can_eat: {can_eat}")
            return result
        else:
            return {
                "has_recent_meal": False,
                "latest_meal": None,
                "interval_minutes": 999,  # 表示很长时间
                "interval_hours": 999,
                "can_eat": True,
                "reason": "今天尚未用餐，可以安排用餐"
            }

    except Exception as e:
        logger.error(f"用餐间隔检查失败: {str(e)}")
        return {
            "has_recent_meal": False,
            "latest_meal": None,
            "interval_minutes": 999,
            "interval_hours": 999,
            "can_eat": True,
            "reason": "用餐间隔检查失败，默认允许用餐"
        }


def _analyze_activity_intensity(daily_activities: list, current_time: str) -> dict:
    """
    分析活动强度，为合理的行程安排提供建议

    Args:
        daily_activities: 当天已安排的活动
        current_time: 当前时间

    Returns:
        dict: 活动强度分析结果
    """
    try:
        from datetime import datetime

        # 解析当前时间
        current_obj = datetime.strptime(current_time, "%H:%M")
        current_minutes = current_obj.hour * 60 + current_obj.minute

        # 分析最近的活动强度
        recent_activities = []
        last_activity = None

        for activity in daily_activities:
            end_time = activity.get("end_time")
            if end_time:
                try:
                    end_obj = datetime.strptime(end_time, "%H:%M")
                    end_minutes = end_obj.hour * 60 + end_obj.minute

                    # 找到最近结束的活动
                    if end_minutes <= current_minutes:
                        activity_info = {
                            "name": activity.get("poi_details", {}).get("name") or activity.get("poi_name", "未知活动"),
                            "type": activity.get("activity_type", "unknown"),
                            "end_time": end_time,
                            "end_minutes": end_minutes
                        }
                        recent_activities.append(activity_info)

                        if last_activity is None or end_minutes > last_activity["end_minutes"]:
                            last_activity = activity_info
                except:
                    continue

        # 分析活动强度和建议
        if last_activity:
            activity_type = last_activity["type"].lower()
            time_since_last = current_minutes - last_activity["end_minutes"]

            # 定义活动强度
            high_intensity_types = ["shopping", "scenic", "attraction"]  # 购物、景点游览
            medium_intensity_types = ["entertainment", "other"]  # 娱乐、其他
            low_intensity_types = ["restaurant", "hotel"]  # 用餐、住宿

            if activity_type in high_intensity_types:
                intensity_level = "high"
                fatigue_level = "tired"
                if time_since_last < 60:  # 1小时内
                    suggestion = "刚完成高强度活动，建议安排用餐或轻松活动作为休息"
                else:
                    suggestion = "可以继续安排活动，但建议避免连续高强度活动"
            elif activity_type in medium_intensity_types:
                intensity_level = "medium"
                fatigue_level = "moderate"
                suggestion = "可以继续安排活动，建议选择不同类型的活动"
            else:
                intensity_level = "low"
                fatigue_level = "refreshed"
                suggestion = "刚完成休息活动，可以安排任何类型的活动"

            # 特殊时间建议和饥饿感知
            if 690 <= current_minutes <= 720:  # 11:30-12:00
                if intensity_level == "high":
                    suggestion = "刚完成高强度活动且接近午餐时间，强烈建议安排午餐休息"
            elif 720 <= current_minutes <= 900:  # 12:00-15:00
                suggestion = "午餐时间窗口，如果今天还没有用餐，必须安排午餐"
            elif current_minutes >= 690 and time_since_last >= 240:  # 11:30之后且距离上次活动4小时
                suggestion = "距离上次活动已久，建议安排用餐休息"

            return {
                "has_recent_activity": True,
                "last_activity": last_activity,
                "intensity_level": intensity_level,
                "fatigue_level": fatigue_level,
                "time_since_last": time_since_last,
                "suggestion": suggestion,
                "should_rest": intensity_level == "high" and time_since_last < 60
            }
        else:
            return {
                "has_recent_activity": False,
                "last_activity": None,
                "intensity_level": "none",
                "fatigue_level": "fresh",
                "time_since_last": 999,
                "suggestion": "今天尚未开始活动，可以安排任何类型的活动",
                "should_rest": False
            }

    except Exception as e:
        logger.error(f"活动强度分析失败: {str(e)}")
        return {
            "has_recent_activity": False,
            "last_activity": None,
            "intensity_level": "unknown",
            "fatigue_level": "unknown",
            "time_since_last": 999,
            "suggestion": "活动强度分析失败，建议谨慎安排活动",
            "should_rest": False
        }


def _is_duplicate_or_similar_poi(poi: dict, daily_activities: list) -> bool:
    """
    检查POI是否为重复或相似的地点

    Args:
        poi: 待检查的POI
        daily_activities: 当天已安排的活动

    Returns:
        bool: 是否为重复或相似的POI
    """
    try:
        poi_name = poi.get("name", "").lower()

        # 定义相似地点的关键词组
        similar_groups = [
            # 故宫相关
            ["故宫博物院", "故宫", "午门", "神武门", "太和殿"],
            # 天安门相关
            ["天安门", "天安门广场", "人民大会堂", "国家博物馆"],
            # 天坛相关
            ["天坛", "天坛公园", "祈年殿", "回音壁"],
            # 颐和园相关
            ["颐和园", "昆明湖", "万寿山", "长廊"],
            # 王府井相关
            ["王府井", "王府井大街", "王府井步行街"],
        ]

        # 检查已访问的活动
        for activity in daily_activities:
            activity_name = ""
            poi_details = activity.get("poi_details", {})
            if poi_details and poi_details.get("name"):
                activity_name = poi_details.get("name", "").lower()
            else:
                activity_name = activity.get("poi_name", "").lower()

            # 完全相同的名称
            if poi_name == activity_name:
                return True

            # 检查是否属于同一相似组
            for group in similar_groups:
                poi_in_group = any(keyword in poi_name for keyword in group)
                activity_in_group = any(keyword in activity_name for keyword in group)

                if poi_in_group and activity_in_group:
                    return True

        return False

    except Exception as e:
        logger.error(f"POI重复检查失败: {str(e)}")
        return False  # 出错时不过滤，保守处理


def _get_time_estimation_hint(poi_name: str, poi_type: str) -> str:
    """
    根据POI名称和类型提供时长估算提示

    Args:
        poi_name: POI名称
        poi_type: POI类型

    Returns:
        str: 时长估算提示
    """
    try:
        poi_name_lower = poi_name.lower()
        poi_type_lower = poi_type.lower()

        # 特殊大型景点的时长提示
        large_attractions = {
            "故宫博物院": "建议4-5小时（大型皇宫建筑群，需要深度游览）",
            "故宫": "建议4-5小时（大型皇宫建筑群，需要深度游览）",
            "紫禁城": "建议4-5小时（大型皇宫建筑群，需要深度游览）",
            "天坛公园": "建议3-4小时（大型古建筑群，包含祈年殿、回音壁等）",
            "天坛": "建议3-4小时（大型古建筑群，包含祈年殿、回音壁等）",
            "颐和园": "建议4-5小时（大型皇家园林，湖光山色需要慢慢欣赏）",
            "圆明园": "建议3-4小时（大型遗址公园，历史文化深厚）",
            "长城": "建议4-6小时（需要爬山，体力消耗大）",
            "八达岭长城": "建议4-6小时（需要爬山，体力消耗大）",
            "慕田峪长城": "建议4-6小时（需要爬山，体力消耗大）",
            "上海博物馆": "建议3-4小时（大型博物馆，文物丰富）",
            "中国国家博物馆": "建议3-4小时（大型博物馆，展品众多）",
            "南京博物院": "建议3-4小时（大型博物馆，历史文物丰富）",
            "西湖": "建议3-4小时（大型风景区，需要环湖游览）",
        }

        # 检查特殊景点
        for attraction_name, hint in large_attractions.items():
            if attraction_name in poi_name:
                return hint

        # 根据POI类型提供通用提示
        if "restaurant" in poi_type_lower or "餐厅" in poi_name:
            return "建议1.5-2小时（用餐时间）"
        elif "scenic" in poi_type_lower or "景点" in poi_type_lower or "风景" in poi_type_lower:
            if "博物馆" in poi_name or "museum" in poi_name_lower:
                return "建议2-3小时（博物馆需要仔细参观）"
            elif "公园" in poi_name or "park" in poi_name_lower:
                return "建议2-3小时（公园适合休闲游览）"
            elif "广场" in poi_name or "square" in poi_name_lower:
                return "建议1-2小时（广场游览和拍照）"
            else:
                return "建议2-3小时（一般景点游览时间）"
        elif "shopping" in poi_type_lower or "购物" in poi_name or "商场" in poi_name:
            return "建议1.5-2.5小时（购物时间）"
        elif "hotel" in poi_type_lower or "酒店" in poi_name:
            return "住宿地点，不计算游览时间"
        else:
            return "建议1.5-2小时（一般活动时间）"

    except Exception as e:
        logger.error(f"时长估算提示生成失败: {str(e)}")
        return "建议2小时（默认时间）"


def _check_hunger_level(daily_activities: list, current_time: str) -> dict:
    """
    检查饥饿程度，提供用餐建议

    Args:
        daily_activities: 当天已安排的活动
        current_time: 当前时间

    Returns:
        dict: 饥饿感知结果
    """
    try:
        from datetime import datetime

        # 解析当前时间
        current_obj = datetime.strptime(current_time, "%H:%M")
        current_minutes = current_obj.hour * 60 + current_obj.minute

        # 检查是否有用餐记录
        has_meals = False
        last_meal_end_minutes = 0
        meal_count = 0

        # 假设酒店早餐时间为8:00-8:30（480-510分钟）
        hotel_breakfast_end = 510  # 8:30

        for activity in daily_activities:
            poi_type = activity.get("poi_type", "").lower()
            activity_type = activity.get("activity_type", "").lower()
            poi_details = activity.get("poi_details", {})
            poi_details_type = poi_details.get("poi_type", "").lower() if poi_details else ""

            is_restaurant = (
                poi_type == "restaurant" or
                activity_type == "restaurant" or
                poi_details_type == "restaurant"
            )

            if is_restaurant:
                has_meals = True
                meal_count += 1
                end_time = activity.get("end_time")
                if end_time:
                    try:
                        end_obj = datetime.strptime(end_time, "%H:%M")
                        end_minutes = end_obj.hour * 60 + end_obj.minute
                        last_meal_end_minutes = max(last_meal_end_minutes, end_minutes)
                    except:
                        continue

        # 计算饥饿程度
        if not has_meals:
            # 没有外出用餐记录，但考虑酒店早餐
            time_since_hotel_breakfast = current_minutes - hotel_breakfast_end

            if current_minutes >= 720 and time_since_hotel_breakfast >= 210:  # 12:00之后且距离酒店早餐3.5小时
                hunger_level = "very_hungry"
                suggestion = "距离酒店早餐已过3.5小时，强烈建议安排午餐"
                should_eat = True
            elif current_minutes >= 690 and time_since_hotel_breakfast >= 180:  # 11:30之后且距离酒店早餐3小时
                hunger_level = "hungry"
                suggestion = "距离酒店早餐已过3小时，建议考虑安排午餐"
                should_eat = True
            elif current_minutes >= 660:  # 11:00之后
                hunger_level = "slightly_hungry"
                suggestion = "接近午餐时间，可以开始考虑用餐"
                should_eat = False
            else:
                hunger_level = "not_hungry"
                suggestion = "刚用过酒店早餐，暂时不需要用餐，专心游览景点"
                should_eat = False
        else:
            # 有用餐记录，检查距离上次用餐的时间
            time_since_last_meal = current_minutes - last_meal_end_minutes

            if time_since_last_meal >= 300:  # 5小时
                hunger_level = "very_hungry"
                suggestion = f"距离上次用餐已过{time_since_last_meal}分钟，非常饿，必须立即用餐"
                should_eat = True
            elif time_since_last_meal >= 240:  # 4小时
                hunger_level = "hungry"
                suggestion = f"距离上次用餐已过{time_since_last_meal}分钟，建议安排用餐"
                should_eat = True
            elif time_since_last_meal >= 120:  # 2小时
                hunger_level = "slightly_hungry"
                suggestion = f"距离上次用餐已过{time_since_last_meal}分钟，可以考虑用餐"
                should_eat = False
            else:
                hunger_level = "not_hungry"
                suggestion = f"距离上次用餐仅过{time_since_last_meal}分钟，暂时不需要用餐"
                should_eat = False

        result = {
            "has_meals": has_meals,
            "meal_count": meal_count,
            "hunger_level": hunger_level,
            "should_eat": should_eat,
            "suggestion": suggestion,
            "current_time_minutes": current_minutes
        }

        print(f"[调试] 饥饿感知检查: {result['suggestion']}, should_eat: {should_eat}")
        return result

    except Exception as e:
        logger.error(f"饥饿感知检查失败: {str(e)}")
        return {
            "has_meals": False,
            "meal_count": 0,
            "hunger_level": "unknown",
            "should_eat": True,  # 出错时建议用餐，保守处理
            "suggestion": "饥饿感知检查失败，建议安排用餐",
            "current_time_minutes": 0
        }
